import { Container } from "@/components/container";
import { HeroSection } from "@/components/hero-section";
import { TeamCard } from "@/components/team-card";
import { FeatureSection } from "@/components/feature-section";
import { StatsSection } from "@/components/stats-section";
import { TestimonialCard } from "@/components/testimonial-card";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Target, 
  Eye, 
  Heart,
  Lightbulb,
  Users,
  Award,
  Rocket,
  Shield,
  Globe
} from "lucide-react";

const AboutPage = () => {
  const team = [
    {
      name: "<PERSON>",
      role: "CEO & Founder",
      bio: "Former Google recruiter with 10+ years of experience in talent acquisition and AI development.",
      image: "/assets/img/hero.jpg",
      skills: ["Leadership", "AI Strategy", "Recruitment"],
      social: {
        linkedin: "https://linkedin.com/in/sarah<PERSON><PERSON>son",
        twitter: "https://twitter.com/sarah<PERSON><PERSON><PERSON>",
        email: "<EMAIL>"
      }
    },
    {
      name: "<PERSON>",
      role: "CT<PERSON>",
      bio: "AI researcher and former Microsoft engineer specializing in natural language processing and machine learning.",
      image: "/assets/img/office.jpg",
      skills: ["AI/ML", "NLP", "System Architecture"],
      social: {
        linkedin: "https://linkedin.com/in/michaelchen",
        email: "<EMAIL>"
      }
    },
    {
      name: "Emily Rodriguez",
      role: "Head of Product",
      bio: "Product strategist with experience at top tech companies, focused on user experience and product innovation.",
      image: "/assets/img/hero.jpg",
      skills: ["Product Strategy", "UX Design", "Analytics"],
      social: {
        linkedin: "https://linkedin.com/in/emilyrodriguez",
        twitter: "https://twitter.com/emilyrodriguez"
      }
    },
    {
      name: "David Kim",
      role: "Head of Engineering",
      bio: "Full-stack engineer and team lead with expertise in scalable systems and modern web technologies.",
      image: "/assets/img/office.jpg",
      skills: ["Full-Stack", "DevOps", "Team Leadership"],
      social: {
        linkedin: "https://linkedin.com/in/davidkim",
        email: "<EMAIL>"
      }
    }
  ];

  const values = [
    {
      icon: Target,
      title: "Excellence",
      description: "We strive for excellence in everything we do, from our AI technology to customer support."
    },
    {
      icon: Heart,
      title: "Empathy",
      description: "We understand the stress of job searching and design our solutions with genuine care for our users."
    },
    {
      icon: Lightbulb,
      title: "Innovation",
      description: "We continuously push the boundaries of AI technology to create better interview experiences."
    },
    {
      icon: Shield,
      title: "Trust",
      description: "We build trust through transparency, reliability, and protecting our users' privacy and data."
    },
    {
      icon: Users,
      title: "Community",
      description: "We foster a supportive community where professionals can grow and succeed together."
    },
    {
      icon: Globe,
      title: "Accessibility",
      description: "We make career development accessible to everyone, regardless of background or location."
    }
  ];

  const stats = [
    { value: "2019", label: "Founded", description: "Started with a vision to democratize interview prep" },
    { value: "50K+", label: "Users Helped", description: "Professionals who've improved their skills" },
    { value: "500+", label: "Companies", description: "Partner with us for talent development" },
    { value: "95%", label: "Success Rate", description: "Users report improved interview performance" }
  ];

  const testimonials = [
    {
      name: "Alex Thompson",
      role: "Software Engineer",
      company: "Amazon",
      image: "/assets/img/hero.jpg",
      testimonial: "This platform completely transformed my interview skills. The AI feedback was incredibly detailed and helped me land my dream job.",
      rating: 5,
      verified: true
    },
    {
      name: "Maria Garcia",
      role: "Product Manager",
      company: "Meta",
      image: "/assets/img/office.jpg",
      testimonial: "The team's dedication to helping job seekers is evident in every feature. Highly recommend to anyone preparing for interviews.",
      rating: 5,
      verified: true
    }
  ];

  return (
    <div className="min-h-screen">
      <Container>
        <HeroSection
          badge="🚀 Our Story"
          title="About Us"
          subtitle="Empowering Careers Through AI"
          description="We're on a mission to democratize interview preparation and help professionals worldwide achieve their career goals through cutting-edge AI technology."
          primaryCta={{
            text: "Join Our Mission",
            href: "/generate"
          }}
          secondaryCta={{
            text: "Meet the Team",
            href: "#team"
          }}
          image="/assets/img/hero.jpg"
        />
      </Container>

      {/* Mission, Vision, Values */}
      <div className="bg-muted/50">
        <Container className="py-16">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            <Card className="text-center">
              <CardContent className="p-8">
                <Target className="w-12 h-12 text-primary mx-auto mb-4" />
                <h3 className="text-2xl font-bold mb-4">Our Mission</h3>
                <p className="text-muted-foreground">
                  To empower every professional with AI-driven tools and insights that transform interview preparation and accelerate career success.
                </p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardContent className="p-8">
                <Eye className="w-12 h-12 text-primary mx-auto mb-4" />
                <h3 className="text-2xl font-bold mb-4">Our Vision</h3>
                <p className="text-muted-foreground">
                  A world where every talented individual has equal access to career opportunities, regardless of their background or circumstances.
                </p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardContent className="p-8">
                <Rocket className="w-12 h-12 text-primary mx-auto mb-4" />
                <h3 className="text-2xl font-bold mb-4">Our Impact</h3>
                <p className="text-muted-foreground">
                  Helping thousands of professionals land their dream jobs while building the future of AI-powered career development.
                </p>
              </CardContent>
            </Card>
          </div>
        </Container>
      </div>

      {/* Company Story */}
      <Container className="py-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <Badge variant="outline" className="mb-4">Our Story</Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              From Frustration to Innovation
            </h2>
            <div className="space-y-4 text-muted-foreground">
              <p>
                Founded in 2019 by a team of former recruiters and AI researchers, our company was born from a simple observation: traditional interview preparation methods weren't keeping up with the evolving job market.
              </p>
              <p>
                After witnessing countless talented individuals struggle with interview anxiety and lack of personalized feedback, we decided to leverage artificial intelligence to create a more effective, accessible, and supportive way to prepare for interviews.
              </p>
              <p>
                Today, we're proud to have helped over 50,000 professionals improve their interview skills and land jobs at top companies worldwide. Our AI-powered platform continues to evolve, incorporating the latest advances in natural language processing and machine learning.
              </p>
            </div>
          </div>
          <div className="relative">
            <img
              src="/assets/img/office.jpg"
              alt="Our office"
              className="rounded-2xl shadow-2xl"
            />
            <div className="absolute -bottom-6 -right-6 bg-primary text-primary-foreground p-6 rounded-xl shadow-lg">
              <div className="text-2xl font-bold">50K+</div>
              <div className="text-sm">Success Stories</div>
            </div>
          </div>
        </div>
      </Container>

      {/* Values */}
      <div className="bg-muted/50">
        <Container>
          <FeatureSection
            title="Our Values"
            subtitle="The principles that guide everything we do and shape our company culture."
            features={values}
          />
        </Container>
      </div>

      {/* Stats */}
      <Container>
        <StatsSection
          title="Our Journey in Numbers"
          subtitle="Milestones that reflect our commitment to helping professionals succeed."
          stats={stats}
        />
      </Container>

      {/* Team */}
      <div className="bg-muted/50" id="team">
        <Container className="py-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Meet Our Team</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Passionate professionals dedicated to revolutionizing career development through AI innovation.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <TeamCard key={index} {...member} />
            ))}
          </div>
        </Container>
      </div>

      {/* Testimonials */}
      <Container className="py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">What People Say</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Hear from professionals who have transformed their careers with our platform.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {testimonials.map((testimonial, index) => (
            <TestimonialCard key={index} {...testimonial} />
          ))}
        </div>
      </Container>
    </div>
  );
};

export default AboutPage;
